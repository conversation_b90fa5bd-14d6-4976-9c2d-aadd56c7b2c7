# DeerFlow多Agent任务数据流汇聚前端展示技术方案

## 1. 项目架构分析

### 1.1 核心技术栈
- **后端**: Python 3.12+ + FastAPI + LangGraph + LangChain
- **前端**: Next.js 22+ + TypeScript + Zustand + TailwindCSS
- **多Agent框架**: LangGraph状态机 + LangChain工具链
- **通信协议**: Server-Sent Events (SSE) + WebSocket
- **状态管理**: Zustand (前端) + LangGraph State (后端)

### 1.2 多Agent系统架构

基于代码分析，DeerFlow采用以下多Agent架构：

```mermaid
graph TD
    A[Coordinator] --> B[Planner]
    B --> C[Human Feedback]
    C --> D[Research Team]
    D --> E[Researcher Agent]
    D --> F[Coder Agent]
    E --> G[Reporter]
    F --> G
    G --> H[Final Report]
```

**核心Agent角色**:
- **Coordinator**: 协调器，管理工作流生命周期
- **Planner**: 规划器，任务分解和执行计划制定
- **Researcher**: 研究员，网络搜索和信息收集
- **Coder**: 编程员，代码分析和Python执行
- **Reporter**: 报告员，结果汇总和报告生成

## 2. 数据流架构设计

### 2.1 后端数据流

#### 2.1.1 LangGraph状态管理
```python
# 基于src/graph/types.py
class State(MessagesState):
    locale: str = "en-US"
    research_topic: str = ""
    observations: list[str] = []
    resources: list[Resource] = []
    plan_iterations: int = 0
    current_plan: Plan | str = None
    final_report: str = ""
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    background_investigation_results: str = None
```

#### 2.1.2 Agent间消息传递
- **消息类型**: `MessageChunkEvent`, `ToolCallsEvent`, `InterruptEvent`
- **状态同步**: 通过LangGraph的`MessagesState`实现
- **工具调用**: 基于LangChain工具链，支持MCP协议扩展

#### 2.1.3 SSE流式数据传输
```python
# 基于src/server/app.py的实现
async def _astream_workflow_generator():
    async for event_data in graph.astream(
        input=initial_state, 
        config=config, 
        stream_mode=["messages", "updates"],
        subgraphs=True
    ):
        # 实时推送Agent执行状态和结果
        yield _make_event(event_type, event_data)
```

### 2.2 前端数据流

#### 2.2.1 状态管理架构
```typescript
// 基于web/src/core/store/store.ts
interface StoreState {
  responding: boolean;
  threadId: string;
  messageIds: string[];
  messages: Map<string, Message>;
  researchIds: string[];
  researchPlanIds: Map<string, string>;
  researchReportIds: Map<string, string>;
  researchActivityIds: Map<string, string[]>;
  ongoingResearchId: string | null;
}
```

#### 2.2.2 实时数据接收
```typescript
// 基于web/src/core/sse/fetch-stream.ts
async function* fetchStream(url: string, init: RequestInit) {
  const response = await fetch(url, { method: "POST" });
  const reader = response.body?.pipeThrough(new TextDecoderStream()).getReader();
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    // 解析SSE事件并更新状态
    const event = parseEvent(chunk);
    yield event;
  }
}
```

## 3. 多Agent任务数据流汇聚方案

### 3.1 数据汇聚层设计

#### 3.1.1 Agent执行状态追踪
```python
# 扩展现有State结构
class EnhancedState(State):
    agent_execution_log: Dict[str, List[AgentExecution]] = {}
    task_dependency_graph: Dict[str, List[str]] = {}
    real_time_metrics: Dict[str, Any] = {}
    
class AgentExecution:
    agent_id: str
    task_id: str
    start_time: datetime
    end_time: Optional[datetime]
    status: ExecutionStatus
    input_data: Any
    output_data: Any
    tool_calls: List[ToolCall]
    error_info: Optional[str]
```

#### 3.1.2 任务依赖关系管理
```python
class TaskDependencyManager:
    def __init__(self):
        self.dependency_graph = nx.DiGraph()
        self.execution_queue = asyncio.Queue()
    
    async def register_task_dependency(self, task_id: str, dependencies: List[str]):
        """注册任务依赖关系"""
        
    async def notify_task_completion(self, task_id: str, result: Any):
        """通知任务完成，触发依赖任务执行"""
```

### 3.2 实时数据聚合

#### 3.2.1 Agent数据收集器
```python
class AgentDataCollector:
    def __init__(self):
        self.agent_outputs = {}
        self.execution_timeline = []
        
    async def collect_agent_output(self, agent_id: str, output: Any):
        """收集Agent输出数据"""
        
    async def aggregate_research_results(self) -> ResearchSummary:
        """聚合研究结果"""
        
    async def generate_execution_report(self) -> ExecutionReport:
        """生成执行报告"""
```

#### 3.2.2 数据流水线
```python
class DataPipeline:
    def __init__(self):
        self.processors = []
        self.filters = []
        
    async def process_agent_data(self, data: AgentData) -> ProcessedData:
        """处理Agent数据"""
        for processor in self.processors:
            data = await processor.process(data)
        return data
        
    async def filter_relevant_data(self, data: ProcessedData) -> FilteredData:
        """过滤相关数据"""
```

## 4. 前端展示层设计

### 4.1 多Agent可视化组件

#### 4.1.1 Agent执行状态面板
```typescript
interface AgentStatusPanelProps {
  agents: Agent[];
  executionStatus: Map<string, ExecutionStatus>;
  realTimeMetrics: RealTimeMetrics;
}

const AgentStatusPanel: React.FC<AgentStatusPanelProps> = ({
  agents,
  executionStatus,
  realTimeMetrics
}) => {
  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {agents.map(agent => (
        <AgentCard 
          key={agent.id}
          agent={agent}
          status={executionStatus.get(agent.id)}
          metrics={realTimeMetrics[agent.id]}
        />
      ))}
    </div>
  );
};
```

#### 4.1.2 任务流程图
```typescript
interface TaskFlowDiagramProps {
  tasks: Task[];
  dependencies: TaskDependency[];
  currentExecution: string[];
}

const TaskFlowDiagram: React.FC<TaskFlowDiagramProps> = ({
  tasks,
  dependencies,
  currentExecution
}) => {
  // 使用React Flow或类似库实现任务流程可视化
  return (
    <ReactFlow
      nodes={taskNodes}
      edges={dependencyEdges}
      onNodeClick={handleTaskClick}
    />
  );
};
```

### 4.2 实时数据展示

#### 4.2.1 数据流监控面板
```typescript
const DataFlowMonitor: React.FC = () => {
  const { messages, responding } = useStore();
  const [agentMetrics, setAgentMetrics] = useState<AgentMetrics>({});
  
  useEffect(() => {
    // 监听SSE数据流
    const eventSource = new EventSource('/api/agent-metrics');
    eventSource.onmessage = (event) => {
      const metrics = JSON.parse(event.data);
      setAgentMetrics(prev => ({ ...prev, ...metrics }));
    };
    
    return () => eventSource.close();
  }, []);
  
  return (
    <div className="space-y-6">
      <MetricsOverview metrics={agentMetrics} />
      <AgentExecutionTimeline />
      <DataFlowVisualization />
    </div>
  );
};
```

#### 4.2.2 结果汇聚展示
```typescript
const ResultAggregationView: React.FC = () => {
  const { researchResults, analysisResults } = useResearchStore();
  
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>研究结果汇总</CardTitle>
        </CardHeader>
        <CardContent>
          <ResearchResultsSummary results={researchResults} />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>分析结果汇总</CardTitle>
        </CardHeader>
        <CardContent>
          <AnalysisResultsSummary results={analysisResults} />
        </CardContent>
      </Card>
    </div>
  );
};
```

## 5. 技术实现细节

### 5.1 后端扩展

#### 5.1.1 增强的Agent节点
```python
# 扩展src/graph/nodes.py
async def enhanced_researcher_node(state: EnhancedState, config: RunnableConfig):
    """增强的研究员节点，支持数据汇聚"""
    
    # 记录开始执行
    execution_id = str(uuid4())
    state.agent_execution_log["researcher"].append(
        AgentExecution(
            agent_id="researcher",
            task_id=execution_id,
            start_time=datetime.now(),
            status=ExecutionStatus.RUNNING
        )
    )
    
    # 执行原有逻辑
    result = await original_researcher_node(state, config)
    
    # 记录执行结果
    execution = state.agent_execution_log["researcher"][-1]
    execution.end_time = datetime.now()
    execution.status = ExecutionStatus.COMPLETED
    execution.output_data = result
    
    # 触发数据汇聚
    await trigger_data_aggregation(state, "researcher", result)
    
    return result
```

#### 5.1.2 数据汇聚API端点
```python
@app.get("/api/agent-metrics")
async def get_agent_metrics():
    """获取Agent执行指标"""
    return StreamingResponse(
        agent_metrics_generator(),
        media_type="text/event-stream"
    )

async def agent_metrics_generator():
    """Agent指标数据生成器"""
    while True:
        metrics = await collect_current_metrics()
        yield f"data: {json.dumps(metrics)}\n\n"
        await asyncio.sleep(1)
```

### 5.2 前端扩展

#### 5.2.1 增强的状态管理
```typescript
// 扩展web/src/core/store/store.ts
interface EnhancedStoreState extends StoreState {
  agentExecutions: Map<string, AgentExecution[]>;
  taskDependencies: TaskDependency[];
  realTimeMetrics: RealTimeMetrics;
  dataAggregationResults: AggregationResult[];
}

const useEnhancedStore = create<EnhancedStoreState>((set, get) => ({
  ...originalStoreState,
  
  updateAgentExecution: (agentId: string, execution: AgentExecution) => {
    set(state => ({
      agentExecutions: new Map(state.agentExecutions).set(
        agentId, 
        [...(state.agentExecutions.get(agentId) || []), execution]
      )
    }));
  },
  
  aggregateResults: (results: AggregationResult[]) => {
    set({ dataAggregationResults: results });
  }
}));
```

#### 5.2.2 实时数据同步Hook
```typescript
const useRealTimeAgentData = () => {
  const [agentData, setAgentData] = useState<AgentData>({});
  const { updateAgentExecution } = useEnhancedStore();
  
  useEffect(() => {
    const eventSource = new EventSource('/api/agent-metrics');
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setAgentData(prev => ({ ...prev, ...data }));
      
      // 更新Agent执行状态
      if (data.executions) {
        Object.entries(data.executions).forEach(([agentId, execution]) => {
          updateAgentExecution(agentId, execution as AgentExecution);
        });
      }
    };
    
    return () => eventSource.close();
  }, [updateAgentExecution]);
  
  return agentData;
};
```

## 6. 部署和监控

### 6.1 容器化部署
```dockerfile
# 基于现有Dockerfile扩展
FROM python:3.12-slim

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制应用代码
COPY src/ /app/src/
COPY web/dist/ /app/web/

# 启动服务
CMD ["uvicorn", "src.server:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 6.2 监控指标
- Agent执行时间和成功率
- 数据流吞吐量
- 任务完成率
- 系统资源使用情况
- 用户交互响应时间

## 7. 性能优化

### 7.1 数据流优化
- 使用Redis缓存中间结果
- 实现数据分片和并行处理
- 优化SSE连接池管理

### 7.2 前端性能优化
- 虚拟滚动处理大量数据
- 使用Web Workers处理数据计算
- 实现智能数据预加载

## 8. 安全考虑

### 8.1 数据安全
- Agent间通信加密
- 敏感数据脱敏处理
- 访问权限控制

### 8.2 系统安全
- API接口认证授权
- 防止Agent执行恶意代码
- 数据传输安全

## 9. 多任务SSE数据帧设计详解

### 9.1 SSE数据帧结构设计

#### 9.1.1 基础数据帧格式
```typescript
// 基于现有的ChatEvent扩展
interface MultiTaskSSEFrame {
  // SSE标准字段
  event: string;           // 事件类型
  data: string;           // JSON序列化的数据
  id?: string;            // 事件ID
  retry?: number;         // 重试间隔

  // 扩展字段（在data中）
  timestamp: number;      // 时间戳
  session_id: string;     // 会话ID
  thread_id: string;      // 线程ID
}

// 多任务数据载荷结构
interface MultiTaskEventData {
  // 任务标识
  task_id: string;
  parent_task_id?: string;
  task_type: TaskType;

  // Agent信息
  agent_id: string;
  agent_type: AgentType;

  // 执行状态
  execution_status: ExecutionStatus;
  progress: number;       // 0-100

  // 数据内容
  content?: any;
  metadata?: Record<string, any>;

  // 关联信息
  dependencies: string[]; // 依赖的任务ID
  children: string[];     // 子任务ID

  // 错误信息
  error?: ErrorInfo;
}
```

#### 9.1.2 具体事件类型定义
```typescript
// 扩展现有的事件类型
enum MultiTaskEventType {
  // 原有事件类型
  MESSAGE_CHUNK = "message_chunk",
  TOOL_CALLS = "tool_calls",
  TOOL_CALL_RESULT = "tool_call_result",
  INTERRUPT = "interrupt",

  // 新增多任务事件类型
  TASK_CREATED = "task_created",           // 任务创建
  TASK_STARTED = "task_started",           // 任务开始
  TASK_PROGRESS = "task_progress",         // 任务进度更新
  TASK_COMPLETED = "task_completed",       // 任务完成
  TASK_FAILED = "task_failed",            // 任务失败
  TASK_DEPENDENCY_RESOLVED = "task_dependency_resolved", // 依赖解析

  // Agent状态事件
  AGENT_STATUS_CHANGED = "agent_status_changed",
  AGENT_TOOL_INVOKED = "agent_tool_invoked",
  AGENT_RESULT_GENERATED = "agent_result_generated",

  // 数据汇聚事件
  DATA_AGGREGATION_STARTED = "data_aggregation_started",
  DATA_AGGREGATION_COMPLETED = "data_aggregation_completed",
  RESEARCH_SUMMARY_UPDATED = "research_summary_updated",

  // 系统事件
  WORKFLOW_STATE_CHANGED = "workflow_state_changed",
  METRICS_UPDATED = "metrics_updated"
}
```

#### 9.1.3 任务状态数据结构
```typescript
interface TaskExecutionFrame {
  event: "task_progress";
  data: {
    task_id: string;
    agent_id: string;
    agent_type: "coordinator" | "planner" | "researcher" | "coder" | "reporter";

    // 执行信息
    execution: {
      status: "pending" | "running" | "completed" | "failed" | "cancelled";
      progress: number;
      start_time: string;
      end_time?: string;
      duration?: number;
    };

    // 任务内容
    task: {
      type: string;
      description: string;
      input: any;
      output?: any;
      tools_used: string[];
    };

    // 关联关系
    relationships: {
      parent_task_id?: string;
      child_task_ids: string[];
      dependency_task_ids: string[];
      blocking_task_ids: string[];
    };

    // 性能指标
    metrics: {
      cpu_usage?: number;
      memory_usage?: number;
      api_calls: number;
      tokens_consumed: number;
    };
  };
}
```

### 9.2 后端SSE数据生成机制

#### 9.2.1 增强的数据生成器
```python
# 扩展src/server/app.py中的_astream_workflow_generator
async def enhanced_multi_task_generator(
    messages: List[dict],
    thread_id: str,
    config: dict
):
    """增强的多任务SSE数据生成器"""

    # 初始化任务追踪器
    task_tracker = MultiTaskTracker(thread_id)

    # 初始化数据汇聚器
    data_aggregator = DataAggregator()

    # 启动性能监控
    metrics_collector = MetricsCollector()

    try:
        # 创建增强的图实例
        enhanced_graph = build_enhanced_graph_with_tracking()

        async for event_data in enhanced_graph.astream(
            input=initial_state,
            config=config,
            stream_mode=["messages", "updates", "debug"],
            subgraphs=True
        ):
            # 处理原有事件
            if isinstance(event_data, tuple):
                message, metadata = event_data

                # 提取任务信息
                task_info = extract_task_info(message, metadata)

                # 更新任务状态
                await task_tracker.update_task_status(task_info)

                # 生成任务状态事件
                yield create_task_status_event(task_info, thread_id)

                # 生成原有消息事件
                yield create_message_event(message, metadata, thread_id)

            # 处理状态更新事件
            elif isinstance(event_data, dict):
                if "__interrupt__" in event_data:
                    yield create_interrupt_event(event_data, thread_id)
                elif "agent_status" in event_data:
                    yield create_agent_status_event(event_data, thread_id)
                elif "task_completed" in event_data:
                    # 任务完成，触发数据汇聚
                    aggregation_result = await data_aggregator.aggregate_task_result(
                        event_data["task_completed"]
                    )
                    yield create_aggregation_event(aggregation_result, thread_id)

            # 定期发送性能指标
            if metrics_collector.should_emit_metrics():
                metrics = await metrics_collector.collect_current_metrics()
                yield create_metrics_event(metrics, thread_id)

    except Exception as e:
        # 发送错误事件
        yield create_error_event(str(e), thread_id)
    finally:
        # 发送完成事件
        yield create_completion_event(thread_id)

def create_task_status_event(task_info: TaskInfo, thread_id: str) -> str:
    """创建任务状态SSE事件"""
    event_data = {
        "task_id": task_info.task_id,
        "agent_id": task_info.agent_id,
        "agent_type": task_info.agent_type,
        "execution": {
            "status": task_info.status,
            "progress": task_info.progress,
            "start_time": task_info.start_time.isoformat(),
            "end_time": task_info.end_time.isoformat() if task_info.end_time else None,
        },
        "task": {
            "type": task_info.task_type,
            "description": task_info.description,
            "input": task_info.input_data,
            "output": task_info.output_data,
            "tools_used": task_info.tools_used,
        },
        "relationships": {
            "parent_task_id": task_info.parent_task_id,
            "child_task_ids": task_info.child_task_ids,
            "dependency_task_ids": task_info.dependency_task_ids,
        },
        "metrics": task_info.metrics,
        "thread_id": thread_id,
        "timestamp": datetime.now().isoformat(),
    }

    return f"event: task_progress\ndata: {json.dumps(event_data)}\nid: {task_info.task_id}\n\n"
```

#### 9.2.2 任务追踪器实现
```python
class MultiTaskTracker:
    """多任务状态追踪器"""

    def __init__(self, thread_id: str):
        self.thread_id = thread_id
        self.tasks: Dict[str, TaskInfo] = {}
        self.task_dependencies: Dict[str, List[str]] = {}
        self.execution_timeline: List[TaskEvent] = []

    async def create_task(self, agent_id: str, task_type: str, description: str) -> str:
        """创建新任务"""
        task_id = f"{agent_id}_{int(time.time() * 1000)}"

        task_info = TaskInfo(
            task_id=task_id,
            agent_id=agent_id,
            task_type=task_type,
            description=description,
            status="pending",
            start_time=datetime.now(),
            progress=0
        )

        self.tasks[task_id] = task_info

        # 记录事件
        self.execution_timeline.append(
            TaskEvent("task_created", task_id, datetime.now())
        )

        return task_id

    async def update_task_status(self, task_info: TaskInfo):
        """更新任务状态"""
        if task_info.task_id in self.tasks:
            old_task = self.tasks[task_info.task_id]

            # 更新状态
            self.tasks[task_info.task_id] = task_info

            # 记录状态变化事件
            if old_task.status != task_info.status:
                self.execution_timeline.append(
                    TaskEvent("status_changed", task_info.task_id, datetime.now(), {
                        "old_status": old_task.status,
                        "new_status": task_info.status
                    })
                )

    async def resolve_dependencies(self, task_id: str) -> List[str]:
        """解析任务依赖"""
        if task_id not in self.task_dependencies:
            return []

        resolved_dependencies = []
        for dep_task_id in self.task_dependencies[task_id]:
            if dep_task_id in self.tasks and self.tasks[dep_task_id].status == "completed":
                resolved_dependencies.append(dep_task_id)

        return resolved_dependencies

    def get_task_hierarchy(self) -> Dict[str, Any]:
        """获取任务层次结构"""
        hierarchy = {}
        for task_id, task_info in self.tasks.items():
            if not task_info.parent_task_id:  # 根任务
                hierarchy[task_id] = self._build_task_tree(task_id)
        return hierarchy

    def _build_task_tree(self, task_id: str) -> Dict[str, Any]:
        """构建任务树"""
        task_info = self.tasks[task_id]
        return {
            "task_info": task_info,
            "children": {
                child_id: self._build_task_tree(child_id)
                for child_id in task_info.child_task_ids
                if child_id in self.tasks
            }
        }
```

#### 9.2.3 数据汇聚器实现
```python
class DataAggregator:
    """数据汇聚器"""

    def __init__(self):
        self.research_results: Dict[str, Any] = {}
        self.analysis_results: Dict[str, Any] = {}
        self.aggregation_rules: List[AggregationRule] = []

    async def aggregate_task_result(self, task_result: TaskResult) -> AggregationResult:
        """汇聚任务结果"""

        # 根据任务类型进行不同的汇聚处理
        if task_result.agent_type == "researcher":
            return await self._aggregate_research_result(task_result)
        elif task_result.agent_type == "coder":
            return await self._aggregate_code_result(task_result)
        elif task_result.agent_type == "reporter":
            return await self._aggregate_report_result(task_result)

        return AggregationResult(
            aggregation_id=str(uuid4()),
            source_task_id=task_result.task_id,
            aggregation_type="generic",
            result=task_result.output_data,
            timestamp=datetime.now()
        )

    async def _aggregate_research_result(self, task_result: TaskResult) -> AggregationResult:
        """汇聚研究结果"""
        research_data = task_result.output_data

        # 提取关键信息
        key_findings = self._extract_key_findings(research_data)
        sources = self._extract_sources(research_data)
        confidence_score = self._calculate_confidence(research_data)

        # 更新全局研究结果
        self.research_results[task_result.task_id] = {
            "findings": key_findings,
            "sources": sources,
            "confidence": confidence_score,
            "timestamp": datetime.now().isoformat()
        }

        # 生成汇聚结果
        return AggregationResult(
            aggregation_id=str(uuid4()),
            source_task_id=task_result.task_id,
            aggregation_type="research",
            result={
                "summary": self._generate_research_summary(),
                "key_findings": key_findings,
                "total_sources": len(sources),
                "confidence_score": confidence_score
            },
            timestamp=datetime.now()
        )

    def _generate_research_summary(self) -> str:
        """生成研究摘要"""
        all_findings = []
        for result in self.research_results.values():
            all_findings.extend(result["findings"])

        # 使用LLM生成摘要（简化实现）
        return f"基于{len(self.research_results)}个研究任务，发现{len(all_findings)}个关键发现"
```

### 9.3 前端SSE数据消费机制

#### 9.3.1 增强的SSE客户端
```typescript
// 扩展web/src/core/sse/fetch-stream.ts
class MultiTaskSSEClient {
  private eventSource: EventSource | null = null;
  private eventHandlers: Map<string, EventHandler[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(private url: string, private config: SSEConfig) {}

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.eventSource = new EventSource(this.url);

      this.eventSource.onopen = () => {
        console.log('SSE连接已建立');
        this.reconnectAttempts = 0;
        resolve();
      };

      this.eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error);
        this.handleReconnect();
      };

      // 注册事件监听器
      this.registerEventListeners();
    });
  }

  private registerEventListeners() {
    if (!this.eventSource) return;

    // 任务相关事件
    this.eventSource.addEventListener('task_created', this.handleTaskCreated.bind(this));
    this.eventSource.addEventListener('task_progress', this.handleTaskProgress.bind(this));
    this.eventSource.addEventListener('task_completed', this.handleTaskCompleted.bind(this));
    this.eventSource.addEventListener('task_failed', this.handleTaskFailed.bind(this));

    // Agent相关事件
    this.eventSource.addEventListener('agent_status_changed', this.handleAgentStatusChanged.bind(this));
    this.eventSource.addEventListener('agent_tool_invoked', this.handleAgentToolInvoked.bind(this));

    // 数据汇聚事件
    this.eventSource.addEventListener('data_aggregation_completed', this.handleDataAggregation.bind(this));
    this.eventSource.addEventListener('research_summary_updated', this.handleResearchSummary.bind(this));

    // 系统事件
    this.eventSource.addEventListener('metrics_updated', this.handleMetricsUpdate.bind(this));
    this.eventSource.addEventListener('workflow_state_changed', this.handleWorkflowStateChange.bind(this));
  }

  private handleTaskProgress(event: MessageEvent) {
    const data: TaskExecutionFrame['data'] = JSON.parse(event.data);

    // 更新任务状态
    useTaskStore.getState().updateTaskStatus(data.task_id, {
      status: data.execution.status,
      progress: data.execution.progress,
      agent_id: data.agent_id,
      agent_type: data.agent_type,
      metrics: data.metrics,
      relationships: data.relationships
    });

    // 触发UI更新
    this.emitEvent('task_progress', data);
  }

  private handleDataAggregation(event: MessageEvent) {
    const data = JSON.parse(event.data);

    // 更新汇聚结果
    useAggregationStore.getState().addAggregationResult(data);

    // 触发UI更新
    this.emitEvent('data_aggregation', data);
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // 指数退避

      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, delay);
    } else {
      console.error('SSE重连失败，已达到最大重试次数');
      this.emitEvent('connection_failed', null);
    }
  }

  on(eventType: string, handler: EventHandler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  private emitEvent(eventType: string, data: any) {
    const handlers = this.eventHandlers.get(eventType) || [];
    handlers.forEach(handler => handler(data));
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}
```

#### 9.3.2 任务状态管理Store
```typescript
// 新增任务状态管理
interface TaskState {
  tasks: Map<string, TaskInfo>;
  taskHierarchy: TaskHierarchy;
  executionTimeline: TaskEvent[];
  currentExecutingTasks: Set<string>;
  completedTasks: Set<string>;
  failedTasks: Set<string>;
}

const useTaskStore = create<TaskState & TaskActions>((set, get) => ({
  tasks: new Map(),
  taskHierarchy: {},
  executionTimeline: [],
  currentExecutingTasks: new Set(),
  completedTasks: new Set(),
  failedTasks: new Set(),

  updateTaskStatus: (taskId: string, update: Partial<TaskInfo>) => {
    set(state => {
      const newTasks = new Map(state.tasks);
      const existingTask = newTasks.get(taskId);

      if (existingTask) {
        const updatedTask = { ...existingTask, ...update };
        newTasks.set(taskId, updatedTask);

        // 更新执行状态集合
        const newCurrentExecuting = new Set(state.currentExecutingTasks);
        const newCompleted = new Set(state.completedTasks);
        const newFailed = new Set(state.failedTasks);

        if (update.status === 'running') {
          newCurrentExecuting.add(taskId);
          newCompleted.delete(taskId);
          newFailed.delete(taskId);
        } else if (update.status === 'completed') {
          newCurrentExecuting.delete(taskId);
          newCompleted.add(taskId);
          newFailed.delete(taskId);
        } else if (update.status === 'failed') {
          newCurrentExecuting.delete(taskId);
          newCompleted.delete(taskId);
          newFailed.add(taskId);
        }

        return {
          tasks: newTasks,
          currentExecutingTasks: newCurrentExecuting,
          completedTasks: newCompleted,
          failedTasks: newFailed
        };
      }

      return state;
    });
  },

  addTaskEvent: (event: TaskEvent) => {
    set(state => ({
      executionTimeline: [...state.executionTimeline, event].sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
      )
    }));
  },

  updateTaskHierarchy: (hierarchy: TaskHierarchy) => {
    set({ taskHierarchy: hierarchy });
  },

  getTasksByAgent: (agentId: string) => {
    const { tasks } = get();
    return Array.from(tasks.values()).filter(task => task.agent_id === agentId);
  },

  getTaskProgress: () => {
    const { tasks } = get();
    const allTasks = Array.from(tasks.values());
    const totalTasks = allTasks.length;
    const completedTasks = allTasks.filter(task => task.status === 'completed').length;

    return totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
  }
}));
```

#### 9.3.3 数据汇聚结果管理
```typescript
interface AggregationState {
  aggregationResults: Map<string, AggregationResult>;
  researchSummary: ResearchSummary | null;
  analysisSummary: AnalysisSummary | null;
  realTimeMetrics: RealTimeMetrics;
}

const useAggregationStore = create<AggregationState & AggregationActions>((set, get) => ({
  aggregationResults: new Map(),
  researchSummary: null,
  analysisSummary: null,
  realTimeMetrics: {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    averageExecutionTime: 0,
    throughput: 0
  },

  addAggregationResult: (result: AggregationResult) => {
    set(state => {
      const newResults = new Map(state.aggregationResults);
      newResults.set(result.aggregation_id, result);

      // 根据汇聚类型更新相应的摘要
      if (result.aggregation_type === 'research') {
        return {
          aggregationResults: newResults,
          researchSummary: result.result as ResearchSummary
        };
      } else if (result.aggregation_type === 'analysis') {
        return {
          aggregationResults: newResults,
          analysisSummary: result.result as AnalysisSummary
        };
      }

      return { aggregationResults: newResults };
    });
  },

  updateMetrics: (metrics: RealTimeMetrics) => {
    set({ realTimeMetrics: metrics });
  },

  getAggregationsByType: (type: string) => {
    const { aggregationResults } = get();
    return Array.from(aggregationResults.values()).filter(
      result => result.aggregation_type === type
    );
  }
}));
```

#### 9.3.4 React Hook集成
```typescript
// 多任务数据消费Hook
const useMultiTaskData = () => {
  const [sseClient, setSSEClient] = useState<MultiTaskSSEClient | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');

  const { updateTaskStatus, addTaskEvent } = useTaskStore();
  const { addAggregationResult, updateMetrics } = useAggregationStore();

  useEffect(() => {
    const client = new MultiTaskSSEClient('/api/chat/stream', {
      reconnect: true,
      maxReconnectAttempts: 5
    });

    // 注册事件处理器
    client.on('task_progress', (data) => {
      updateTaskStatus(data.task_id, {
        status: data.execution.status,
        progress: data.execution.progress,
        agent_id: data.agent_id,
        agent_type: data.agent_type,
        metrics: data.metrics
      });
    });

    client.on('data_aggregation', (data) => {
      addAggregationResult(data);
    });

    client.on('metrics_updated', (data) => {
      updateMetrics(data);
    });

    client.on('connection_failed', () => {
      setConnectionStatus('disconnected');
      // 可以在这里显示错误提示
    });

    // 建立连接
    setConnectionStatus('connecting');
    client.connect().then(() => {
      setConnectionStatus('connected');
      setSSEClient(client);
    }).catch(() => {
      setConnectionStatus('disconnected');
    });

    return () => {
      client.disconnect();
    };
  }, [updateTaskStatus, addAggregationResult, updateMetrics]);

  return {
    connectionStatus,
    isConnected: connectionStatus === 'connected'
  };
};

// 任务进度监控Hook
const useTaskProgress = () => {
  const tasks = useTaskStore(state => state.tasks);
  const currentExecutingTasks = useTaskStore(state => state.currentExecutingTasks);
  const getTaskProgress = useTaskStore(state => state.getTaskProgress);

  const overallProgress = useMemo(() => getTaskProgress(), [tasks, getTaskProgress]);

  const tasksByAgent = useMemo(() => {
    const result: Record<string, TaskInfo[]> = {};
    Array.from(tasks.values()).forEach(task => {
      if (!result[task.agent_id]) {
        result[task.agent_id] = [];
      }
      result[task.agent_id].push(task);
    });
    return result;
  }, [tasks]);

  const currentlyExecuting = useMemo(() => {
    return Array.from(currentExecutingTasks).map(taskId => tasks.get(taskId)).filter(Boolean);
  }, [currentExecutingTasks, tasks]);

  return {
    overallProgress,
    tasksByAgent,
    currentlyExecuting,
    totalTasks: tasks.size
  };
};
```

## 总结

本技术方案基于DeerFlow现有架构，通过扩展LangGraph状态管理、增强SSE数据流、优化前端状态管理等方式，实现了多Agent任务数据流的有效汇聚和前端实时展示。方案充分利用了项目现有的技术栈和架构设计，确保了实现的可行性和系统的稳定性。

### 关键技术特点：

1. **完整的SSE数据帧设计**：定义了详细的多任务事件类型和数据结构
2. **后端数据生成机制**：实现了任务追踪器和数据汇聚器
3. **前端消费机制**：提供了完整的SSE客户端和状态管理方案
4. **实时性保证**：通过事件驱动架构确保数据的实时性
5. **可扩展性**：支持新的任务类型和汇聚规则的动态添加
